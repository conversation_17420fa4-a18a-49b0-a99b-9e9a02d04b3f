"""
Arroyo University AI Service Configuration
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Dict, Any


class Settings(BaseSettings):
    """AI Service settings"""
    
    # Application
    APP_NAME: str = Field(default="Arroyo University AI Service")
    APP_VERSION: str = Field(default="1.0.0")
    DEBUG: bool = Field(default=True)
    ENVIRONMENT: str = Field(default="development")
    
    # Database
    DATABASE_URL: str = Field(...)
    
    # Redis/Celery
    REDIS_URL: str = Field(...)
    CELERY_BROKER_URL: str = Field(...)
    CELERY_RESULT_BACKEND: str = Field(...)
    
    # OpenAI
    OPENAI_API_KEY: str = Field(default="")
    OPENAI_MODEL: str = Field(default="gpt-4-turbo-preview")
    OPENAI_MAX_TOKENS: int = Field(default=4000)
    OPENAI_TEMPERATURE: float = Field(default=0.7)
    OPENAI_TIMEOUT: int = Field(default=30)
    OPENAI_MAX_RETRIES: int = Field(default=3)
    
    # Azure Speech
    AZURE_SPEECH_KEY: str = Field(default="")
    AZURE_SPEECH_REGION: str = Field(default="")
    AZURE_SPEECH_ENDPOINT: str = Field(default="")
    AZURE_TTS_VOICE: str = Field(default="en-US-AriaNeural")
    AZURE_STT_LANGUAGE: str = Field(default="en-US")
    
    # Content Moderation
    ENABLE_CONTENT_MODERATION: bool = Field(default=True)
    MODERATION_THRESHOLD: float = Field(default=0.8)
    BLOCKED_CATEGORIES: List[str] = Field(default=["hate", "harassment", "self-harm", "sexual", "violence"])
    
    # AI Limits
    MAX_QUESTIONS_PER_REQUEST: int = Field(default=10)
    MAX_GENERATION_TIME_SECONDS: int = Field(default=120)
    DAILY_GENERATION_LIMIT_PER_TENANT: int = Field(default=1000)
    MONTHLY_GENERATION_LIMIT_PER_TENANT: int = Field(default=10000)
    
    # Scoring
    SCORING_MODEL: str = Field(default="gpt-4-turbo-preview")
    SCORING_TEMPERATURE: float = Field(default=0.1)
    SCORING_MAX_TOKENS: int = Field(default=1000)
    ENABLE_RUBRIC_SCORING: bool = Field(default=True)
    
    # Audio Processing
    MAX_AUDIO_DURATION_SECONDS: int = Field(default=300)
    SUPPORTED_AUDIO_FORMATS: List[str] = Field(default=["mp3", "wav", "m4a", "ogg"])
    AUDIO_SAMPLE_RATE: int = Field(default=16000)
    AUDIO_CHANNELS: int = Field(default=1)
    
    # Plagiarism Detection
    ENABLE_PLAGIARISM_DETECTION: bool = Field(default=True)
    PLAGIARISM_THRESHOLD: float = Field(default=0.7)
    TURNITIN_API_KEY: str = Field(default="")
    TURNITIN_ENDPOINT: str = Field(default="https://api.turnitin.com/")
    
    # Caching
    CACHE_TTL_SECONDS: int = Field(default=3600)
    CACHE_MAX_SIZE: int = Field(default=10000)
    ENABLE_RESPONSE_CACHING: bool = Field(default=True)
    
    # Celery
    CELERY_TASK_SERIALIZER: str = Field(default="json")
    CELERY_RESULT_SERIALIZER: str = Field(default="json")
    CELERY_ACCEPT_CONTENT: List[str] = Field(default=["json"])
    CELERY_TIMEZONE: str = Field(default="UTC")
    CELERY_ENABLE_UTC: bool = Field(default=True)
    CELERY_TASK_TRACK_STARTED: bool = Field(default=True)
    CELERY_TASK_TIME_LIMIT: int = Field(default=300)
    CELERY_TASK_SOFT_TIME_LIMIT: int = Field(default=240)
    CELERY_WORKER_PREFETCH_MULTIPLIER: int = Field(default=1)
    CELERY_WORKER_MAX_TASKS_PER_CHILD: int = Field(default=1000)
    
    # Queue Routes
    CELERY_TASK_ROUTES: Dict[str, Dict[str, str]] = Field(default={
        'app.tasks.generate_questions': {'queue': 'generation'},
        'app.tasks.score_response': {'queue': 'scoring'},
        'app.tasks.moderate_content': {'queue': 'moderation'},
        'app.tasks.process_audio': {'queue': 'audio'}
    })
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True)
    METRICS_PORT: int = Field(default=8001)
    SENTRY_DSN: str = Field(default="")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO")
    LOG_FORMAT: str = Field(default="json")
    LOG_FILE: str = Field(default="/app/logs/ai-service.log")
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=30)
    RATE_LIMIT_BURST: int = Field(default=5)
    
    # File Storage
    TEMP_DIR: str = Field(default="/app/temp")
    MAX_TEMP_FILE_AGE_HOURS: int = Field(default=24)
    CLEANUP_TEMP_FILES: bool = Field(default=True)
    
    # Cost Management
    ENABLE_COST_TRACKING: bool = Field(default=True)
    COST_ALERT_THRESHOLD_USD: float = Field(default=100.0)
    DAILY_COST_LIMIT_USD: float = Field(default=500.0)
    
    # Feature Flags
    ENABLE_QUESTION_GENERATION: bool = Field(default=True)
    ENABLE_AUTOMATIC_SCORING: bool = Field(default=True)
    ENABLE_SPEECH_SYNTHESIS: bool = Field(default=True)
    ENABLE_SPEECH_RECOGNITION: bool = Field(default=True)
    ENABLE_CONTENT_MODERATION: bool = Field(default=True)
    ENABLE_PLAGIARISM_CHECK: bool = Field(default=True)
    
    # Performance
    MAX_CONCURRENT_REQUESTS: int = Field(default=10)
    REQUEST_TIMEOUT_SECONDS: int = Field(default=60)
    CONNECTION_POOL_SIZE: int = Field(default=20)
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
