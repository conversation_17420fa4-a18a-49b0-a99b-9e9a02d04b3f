import { Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { ThemeProvider } from '@/components/theme-provider'
import Layout from '@/components/layout/Layout'

// Auth Pages
import LoginPage from '@/pages/auth/LoginPage'

// Main Pages
import HomePage from '@/pages/HomePage'
import MarketplacePage from '@/pages/MarketplacePage'
import CareerPathsPage from '@/pages/CareerPathsPage'
import LeaderboardPage from '@/pages/LeaderboardPage'
import MyCoursesPage from '@/pages/MyCoursesPage'
import SavedCoursesPage from '@/pages/SavedCoursesPage'
import AnalyticsPage from '@/pages/AnalyticsPage'
import ProfilePage from '@/pages/ProfilePage'
import SettingsPage from '@/pages/SettingsPage'
import NotFoundPage from '@/pages/NotFoundPage'

// Admin Pages
import CourseCreationPage from '@/pages/admin/CourseCreationPage'
import GroupManagementPage from '@/pages/admin/GroupManagementPage'
import UserManagementPage from '@/pages/admin/UserManagementPage'

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="arroyo-ui-theme">
      <div className="min-h-screen bg-background font-sans antialiased">
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />

          {/* Protected routes with layout */}
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="marketplace" element={<MarketplacePage />} />
            <Route path="career-paths" element={<CareerPathsPage />} />
            <Route path="leaderboard" element={<LeaderboardPage />} />
            <Route path="my-courses" element={<MyCoursesPage />} />
            <Route path="saved" element={<SavedCoursesPage />} />
            <Route path="analytics" element={<AnalyticsPage />} />
            <Route path="profile" element={<ProfilePage />} />
            <Route path="settings" element={<SettingsPage />} />

            {/* Admin routes */}
            <Route path="admin/courses/create" element={<CourseCreationPage />} />
            <Route path="admin/groups" element={<GroupManagementPage />} />
            <Route path="admin/users" element={<UserManagementPage />} />
          </Route>

          {/* 404 page */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        <Toaster position="top-right" />
      </div>
    </ThemeProvider>
  )
}

export default App
