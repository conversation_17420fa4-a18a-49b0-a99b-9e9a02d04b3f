# Arroyo University - Project Structure

## Overview

This document provides a comprehensive overview of the Arroyo University project structure, based on the documentation and mockups provided. The platform follows a microservices architecture with Docker Compose orchestration for local development.

## Architecture Summary

### Core Services
1. **Frontend SPA** - React 18 with Vite, Tailwind CSS, PWA capabilities
2. **API Gateway** - Nginx-based routing, rate limiting, SSL termination  
3. **Core API** - FastAPI with SQLModel, handles CRUD, RBAC, multi-tenant enforcement
4. **AI Service** - FastAPI with Celery for AI operations (question generation, scoring, moderation)
5. **Notification Service** - Handles emails, webhooks, real-time notifications
6. **PostgreSQL Database** - Primary database with multi-tenant support and RLS
7. **Redis** - Message queue and caching
8. **MinIO** - Object storage for multimedia files (local development)
9. **Monitoring Stack** - Prometheus, Grafana, Loki for observability

## Directory Structure

```
ArroyoUniversity/
├── docker-compose.yml              # Main orchestration file
├── docker-compose.prod.yml         # Production overrides
├── .env                            # Root environment configuration
├── .env.example                    # Environment template
├── .gitignore                      # Git ignore rules
├── Makefile                        # Development commands
├── README.md                       # Project documentation
├── PROJECT_STRUCTURE.md            # This file
│
├── api-gateway/                    # Nginx-based API Gateway
│   ├── Dockerfile                  # Container definition
│   ├── .env                        # Service-specific environment
│   ├── nginx.conf                  # Nginx configuration
│   └── ssl/                        # SSL certificates (to be added)
│
├── core-api/                       # Main backend API service
│   ├── Dockerfile                  # Container definition
│   ├── .env                        # Service-specific environment
│   ├── requirements.txt            # Python dependencies
│   └── app/                        # Application code
│       ├── main.py                 # FastAPI application entry point
│       ├── config.py               # Configuration management
│       ├── database.py             # Database connection
│       ├── models/                 # SQLModel models (to be created)
│       ├── routers/                # API route handlers (to be created)
│       ├── services/               # Business logic (to be created)
│       ├── utils/                  # Utility functions (to be created)
│       └── tests/                  # Unit tests (to be created)
│
├── ai-service/                     # AI processing service
│   ├── Dockerfile                  # Container definition
│   ├── .env                        # Service-specific environment
│   ├── requirements.txt            # Python dependencies
│   └── app/                        # Application code
│       ├── main.py                 # FastAPI application entry point
│       ├── config.py               # Configuration management
│       ├── celery_app.py           # Celery configuration (to be created)
│       ├── tasks/                  # Celery tasks (to be created)
│       ├── services/               # AI service implementations (to be created)
│       └── tests/                  # Unit tests (to be created)
│
├── notification-service/           # Notification handling service
│   ├── Dockerfile                  # Container definition
│   ├── .env                        # Service-specific environment
│   ├── requirements.txt            # Python dependencies
│   └── app/                        # Application code
│       ├── main.py                 # FastAPI application entry point
│       ├── config.py               # Configuration management
│       ├── services/               # Notification implementations (to be created)
│       ├── templates/              # Email templates (to be created)
│       └── tests/                  # Unit tests (to be created)
│
├── frontend/                       # React SPA
│   ├── Dockerfile                  # Multi-stage container definition
│   ├── .env                        # Service-specific environment
│   ├── package.json                # Node.js dependencies
│   ├── vite.config.ts              # Vite configuration
│   ├── index.html                  # HTML entry point
│   └── src/                        # Source code
│       ├── main.tsx                # React application entry point
│       ├── App.tsx                 # Main App component
│       ├── components/             # Reusable components (to be created)
│       ├── pages/                  # Page components (to be created)
│       ├── hooks/                  # Custom React hooks (to be created)
│       ├── services/               # API services (to be created)
│       ├── store/                  # State management (to be created)
│       ├── types/                  # TypeScript types (to be created)
│       └── utils/                  # Utility functions (to be created)
│
├── database/                       # Database configuration
│   └── init/
│       └── 01-init.sql             # Initial database schema
│
├── monitoring/                     # Monitoring configuration
│   ├── prometheus/
│   │   ├── prometheus.yml          # Prometheus configuration
│   │   └── alert_rules.yml         # Alert rules
│   ├── grafana/
│   │   └── provisioning/           # Grafana dashboards (to be created)
│   └── loki/
│       └── local-config.yaml       # Loki configuration
│
├── documentation/                  # Project documentation (existing)
│   ├── 01_Vision_y_Estrategia.md
│   ├── 02_Alcance_y_Roadmap.md
│   ├── 03_Arquitectura_Sistema.md
│   ├── 04_Modelo_Datos.md
│   ├── 05_Seguridad_RBAC.md
│   ├── 06_Requisitos_Funcionales.md
│   ├── 07_Flujos_Procesos.md
│   ├── 08_Integracion_IA.md
│   ├── 09_API_Reference.md
│   ├── 10_Requisitos_No_Funcionales.md
│   ├── 11_Infraestructura_DevOps.md
│   ├── 12_Cumplimiento_Estandares.md
│   ├── 13_Metricas_Validacion.md
│   ├── 14_Extensibilidad_Futuro.md
│   ├── 15_Guia_Implementacion.md
│   ├── 16_Testing_QA.md
│   ├── 17_Sistema_Expertos.md
│   ├── 18_Sistema_Career_Paths.md
│   ├── 19_Sistema_Gamificacion.md
│   └── README.md
│
└── mockups/                        # UI mockups (existing)
    ├── 01_auth_login.html
    ├── 02_tenant_setup_wizard.html
    ├── 03_user_registration.html
    ├── 04_admin_tenant_dashboard.html
    ├── 05_content_creator_dashboard.html
    ├── 06_student_dashboard.html
    ├── 07_system_admin_dashboard.html
    ├── 08_course_creation.html
    ├── 09_question_bank.html
    ├── 10_ai_question_generator.html
    ├── 11_multimedia_upload.html
    ├── 12_exam_creation_wizard.html
    ├── 15_exam_landing.html
    ├── 16_exam_writing_question.html
    ├── 17_exam_listening_question.html
    ├── 18_exam_speaking_question.html
    ├── 20_manual_scoring.html
    ├── 21_results_dashboard.html
    ├── 22_detailed_feedback.html
    ├── 23_analytics_dashboard.html
    ├── 26_user_management.html
    ├── 29_system_settings.html
    ├── 30_create_tenant.html
    ├── 31_tenant_management.html
    ├── 32_system_monitoring.html
    ├── 33_billing_management.html
    ├── 34_exam_proctoring.html
    ├── 35_certificate_generator.html
    ├── 36_api_documentation.html
    ├── 37_mobile_exam_interface.html
    ├── 38_integration_marketplace.html
    ├── 39_course_marketplace.html
    ├── 40_group_management.html
    ├── 41_group_leader_dashboard.html
    ├── 42_role_management.html
    ├── 43_user_home_dashboard.html
    ├── 44_course_administration.html
    ├── 45_course_forum.html
    ├── 46_course_detail_with_expert_review.html
    ├── 47_career_path_marketplace.html
    ├── 48_career_path_builder.html
    ├── 49_leaderboard.html
    ├── README.md
    └── roles.md
```

## Key Features Implemented

### Infrastructure
- ✅ Docker Compose orchestration for all services
- ✅ Multi-environment support (development/production)
- ✅ Health checks for all services
- ✅ Monitoring stack with Prometheus, Grafana, Loki
- ✅ API Gateway with Nginx for routing and rate limiting
- ✅ PostgreSQL with multi-tenant support and RLS
- ✅ Redis for caching and message queuing
- ✅ MinIO for object storage (development)

### Backend Services
- ✅ Core API with FastAPI and SQLModel
- ✅ AI Service with Celery for async processing
- ✅ Notification Service for multi-channel notifications
- ✅ Configuration management with Pydantic Settings
- ✅ Database initialization with proper schema
- ✅ Logging and error handling

### Frontend
- ✅ React 18 with TypeScript
- ✅ Vite for build tooling
- ✅ PWA capabilities
- ✅ Modern UI component structure
- ✅ State management setup

### Development Tools
- ✅ Makefile with common development commands
- ✅ Environment configuration templates
- ✅ Git ignore rules
- ✅ Production deployment configuration

## Next Steps

1. **Implement Core API Features**
   - Authentication and authorization
   - User and tenant management
   - Course management
   - RBAC implementation

2. **Build Frontend Components**
   - Authentication pages
   - Dashboard components
   - Course management UI
   - User management interface

3. **Develop AI Service**
   - Question generation
   - Content scoring
   - Speech processing
   - Content moderation

4. **Complete Notification Service**
   - Email templates
   - Push notifications
   - WebSocket implementation
   - Webhook handling

5. **Add Testing**
   - Unit tests for all services
   - Integration tests
   - End-to-end tests
   - Load testing

6. **Monitoring and Observability**
   - Grafana dashboards
   - Custom metrics
   - Log aggregation
   - Alert configuration

7. **Security Implementation**
   - JWT authentication
   - Rate limiting
   - Input validation
   - Security headers

8. **Documentation**
   - API documentation
   - User guides
   - Deployment guides
   - Architecture documentation

## Getting Started

1. **Clone the repository**
2. **Copy environment configuration**: `cp .env.example .env`
3. **Update environment variables** with your configuration
4. **Start the development environment**: `make dev-setup`
5. **Access the services** at the URLs listed in the README

This structure provides a solid foundation for the Arroyo University platform, following modern microservices best practices and supporting the requirements outlined in the documentation and mockups.
