import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  ShoppingBag,
  TrendingUp,
  Trophy,
  BookOpen,
  Heart,
  BarChart3,
  Settings,
  User,
  HelpCircle,
  Layers,
  Users,
  UserPlus,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/store/authStore';
import { useUIStore } from '@/store/uiStore';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { generateInitials } from '@/lib/utils';

const navigation = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Marketplace', href: '/marketplace', icon: ShoppingBag },
  { name: 'Career Paths', href: '/career-paths', icon: TrendingUp },
  { name: 'Leaderboard', href: '/leaderboard', icon: Trophy },
  { name: 'Mi<PERSON> Cursos', href: '/my-courses', icon: BookOpen },
  { name: 'Guardado<PERSON>', href: '/saved', icon: Heart },
  { name: '<PERSON>lí<PERSON>s', href: '/analytics', icon: BarChart3 },
];

const adminNavigation = [
  { name: 'Crear Curso', href: '/admin/courses/create', icon: Plus },
  { name: 'Administrar Cursos', href: '/admin/courses', icon: Layers },
  { name: 'Banco de Preguntas', href: '/admin/questions', icon: HelpCircle },
  { name: 'Gestión de Usuarios', href: '/admin/users', icon: Users },
  { name: 'Gestión de Grupos', href: '/admin/groups', icon: UserPlus },
];

const profileNavigation = [
  { name: 'Perfil', href: '/profile', icon: User },
  { name: 'Configuración', href: '/settings', icon: Settings },
];

export function Sidebar() {
  const location = useLocation();
  const { user } = useAuthStore();
  const { sidebarOpen } = useUIStore();

  if (!sidebarOpen) return null;

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  // Check if user has admin permissions (simplified check)
  const hasAdminPermissions = user?.metadata?.roles?.includes('admin') || 
                             user?.metadata?.roles?.includes('instructor');

  return (
    <div className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
        {/* Logo */}
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
            </svg>
          </div>
          <h1 className="ml-3 text-lg font-semibold text-gray-900">Arroyo University</h1>
        </div>

        {/* Navigation */}
        <nav className="mt-8 flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  'sidebar-nav-item',
                  isActive(item.href) ? 'active' : ''
                )}
              >
                <Icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            );
          })}

          {/* Admin Section */}
          {hasAdminPermissions && (
            <div className="pt-4 mt-4 border-t border-gray-200">
              <p className="px-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Administración
              </p>
              {adminNavigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={cn(
                      'sidebar-nav-item mt-1',
                      isActive(item.href) ? 'active' : ''
                    )}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          )}

          {/* Profile Section */}
          <div className="pt-4 mt-4 border-t border-gray-200">
            {profileNavigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    'sidebar-nav-item',
                    isActive(item.href) ? 'active' : ''
                  )}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              );
            })}
          </div>
        </nav>

        {/* User Info */}
        <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
          <div className="flex items-center">
            <Avatar className="w-8 h-8">
              <AvatarImage src={user?.avatar} alt={user?.firstName} />
              <AvatarFallback className="bg-blue-500 text-white text-sm font-medium">
                {generateInitials(user?.firstName, user?.lastName)}
              </AvatarFallback>
            </Avatar>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">
                {user?.firstName || user?.username || 'Usuario'}
              </p>
              <p className="text-xs text-gray-500">
                {user?.metadata?.role || 'Usuario'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
